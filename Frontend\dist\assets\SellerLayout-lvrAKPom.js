import{d as S,u as g,e as o,az as m,aA as h,j as e,Q as x,aB as j,R as _,S as u,T as p,h as N,U as y,aC as b,aD as v,W as f,o as k,r as C,aE as M,aF as L}from"./index-Bn4SSHNk.js";const T=()=>{const r=S(),s=g(),a=o(m),i=o(h),l=d=>{switch(r(v(d)),r(b(!1)),d){case"dashboard":s("/seller/dashboard");break;case"my-sports-strategies":s("/seller/my-sports-strategies");break;case"requests":s("/seller/requests");break;case"bids":s("/seller/bids");break;case"cards":s("/seller/cards");break;case"profile":s("/seller/profile");break;default:s("/seller/dashboard")}},t=()=>{r(f()),r(b(!1)),s("/")},c=()=>{r(b(!1))};return e.jsxs("div",{className:`SellerSidebar ${i?"open":""}`,children:[e.jsx("div",{className:"SellerSidebar__overlay",onClick:c}),e.jsx("div",{className:"SellerSidebar__container",children:e.jsxs("ul",{className:"SellerSidebar__menu",children:[e.jsxs("li",{className:`SellerSidebar__item ${a==="dashboard"?"active":""}`,onClick:()=>l("dashboard"),children:[e.jsx(x,{className:"SellerSidebar__icon"}),e.jsx("span",{children:"Dashboard"})]}),e.jsxs("li",{className:`SellerSidebar__item ${a==="my-sports-strategies"?"active":""}`,onClick:()=>l("my-sports-strategies"),children:[e.jsx(j,{className:"SellerSidebar__icon"}),e.jsx("span",{children:"My Sports Strategies"})]}),e.jsxs("li",{className:`SellerSidebar__item ${a==="requests"?"active":""}`,onClick:()=>l("requests"),children:[e.jsx(_,{className:"SellerSidebar__icon"}),e.jsx("span",{children:"Requests"})]}),e.jsxs("li",{className:`SellerSidebar__item ${a==="bids"?"active":""}`,onClick:()=>l("bids"),children:[e.jsx(u,{className:"SellerSidebar__icon"}),e.jsx("span",{children:"Bids"})]}),e.jsxs("li",{className:`SellerSidebar__item ${a==="cards"?"active":""}`,onClick:()=>l("cards"),children:[e.jsx(p,{className:"SellerSidebar__icon"}),e.jsx("span",{children:"My Cards"})]}),e.jsxs("li",{className:`SellerSidebar__item ${a==="profile"?"active":""}`,onClick:()=>l("profile"),children:[e.jsx(N,{className:"SellerSidebar__icon"}),e.jsx("span",{children:"My Profile"})]}),e.jsxs("li",{className:"SellerSidebar__item SellerSidebar__logout",onClick:t,children:[e.jsx(y,{className:"SellerSidebar__icon"}),e.jsx("span",{children:"Logout"})]})]})})]})},$=({children:r})=>{const s=S(),a=k(),i=o(m);o(h);const l={"/seller/dashboard":"dashboard","/seller/my-sports-strategies":"my-sports-strategies","/seller/requests":"requests","/seller/bids":"bids","/seller/cards":"cards","/seller/profile":"profile"},t={dashboard:{title:"Dashboard",icon:e.jsx(x,{})},"my-sports-strategies":{title:"My Sports Strategies",icon:e.jsx(j,{})},requests:{title:"Requests",icon:e.jsx(_,{})},bids:{title:"Bids",icon:e.jsx(u,{})},cards:{title:"My Cards",icon:e.jsx(p,{})},profile:{title:"My Profile",icon:e.jsx(N,{})}},c=t[i]||t.dashboard,d=()=>{s(L())};return C.useEffect(()=>{const n=l[a.pathname];n&&n!==i&&s(v(n))},[a.pathname,i,s]),e.jsx("div",{className:"SellerLayout",children:e.jsxs("div",{className:"container max-container",children:[e.jsx("div",{className:"sidebar",children:e.jsx(T,{})}),e.jsxs("div",{className:"content",children:[e.jsx("div",{className:"bordrdiv mb-30",children:e.jsxs("div",{className:"SellerLayout__header",children:[e.jsx("button",{className:"SellerLayout__mobile-menu-btn",onClick:d,"aria-label":"Toggle mobile menu",children:e.jsx(M,{})}),e.jsxs("h2",{className:"SellerLayout__title",children:[c.icon,c.title]})]})}),e.jsx("div",{className:"SellerLayout__content",children:r})]})]})})};export{$ as S};
