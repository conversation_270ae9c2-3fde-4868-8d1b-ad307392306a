import{d as b,u as G,e as o,P as E,j as e,Q as H,h as x,H as D,R as U,S as L,T as v,U as Q,V as u,W as X,r as c,X as F,f as M,i as Z,Y as J,Z as K,$ as ee,a0 as se,a1 as ae,a2 as R,a3 as re,a4 as ie,a5 as $,a6 as te,a7 as le,a8 as de,a9 as ce,aa as ne,ab as oe,ac as ue,ad as k,ae as A,af as me,ag as f,ah as _e,o as he}from"./index-Bn4SSHNk.js";import{S as p,B as I}from"./BuyerAccountDashboard-BF8-i3pQ.js";import{t as w}from"./toast-JdQfOdwe.js";const ye=()=>{const a=b(),r=G(),s=o(E),t=d=>{switch(a(u(d)),d){case"dashboard":r("/buyer/account/dashboard");break;case"profile":r("/buyer/account/profile");break;case"downloads":r("/buyer/account/downloads");break;case"requests":r("/buyer/account/requests");break;case"bids":r("/buyer/account/bids");break;case"cards":r("/buyer/account/cards");break;case"logout":a(X()),r("/");break;default:r("/buyer/account/dashboard")}};return e.jsx("div",{className:"BuyerSidebar",children:e.jsx("div",{className:"BuyerSidebar__container",children:e.jsxs("ul",{className:"BuyerSidebar__menu",children:[e.jsxs("li",{className:`BuyerSidebar__item ${s==="dashboard"?"active":""}`,onClick:()=>t("dashboard"),children:[e.jsx(H,{className:"BuyerSidebar__icon"}),e.jsx("span",{children:"Dashboard"})]}),e.jsxs("li",{className:`BuyerSidebar__item ${s==="profile"?"active":""}`,onClick:()=>t("profile"),children:[e.jsx(x,{className:"BuyerSidebar__icon"}),e.jsx("span",{children:"My Profile"})]}),e.jsxs("li",{className:`BuyerSidebar__item ${s==="downloads"?"active":""}`,onClick:()=>t("downloads"),children:[e.jsx(D,{className:"BuyerSidebar__icon"}),e.jsx("span",{children:"My Downloads"})]}),e.jsxs("li",{className:`BuyerSidebar__item ${s==="requests"?"active":""}`,onClick:()=>t("requests"),children:[e.jsx(U,{className:"BuyerSidebar__icon"}),e.jsx("span",{children:"My Requests"})]}),e.jsxs("li",{className:`BuyerSidebar__item ${s==="bids"?"active":""}`,onClick:()=>t("bids"),children:[e.jsx(L,{className:"BuyerSidebar__icon"}),e.jsx("span",{children:"My Bids"})]}),e.jsxs("li",{className:`BuyerSidebar__item ${s==="cards"?"active":""}`,onClick:()=>t("cards"),children:[e.jsx(v,{className:"BuyerSidebar__icon"}),e.jsx("span",{children:"My Cards"})]}),e.jsxs("li",{className:"BuyerSidebar__item BuyerSidebar__logout",onClick:()=>t("logout"),children:[e.jsx(Q,{className:"BuyerSidebar__icon"}),e.jsx("span",{children:"Logout"})]})]})})})},xe=()=>{const a=b(),{user:r,isLoading:s,isSuccess:t,isError:d,error:h}=o(m=>m.auth),[l,B]=c.useState({firstName:"",lastName:"",email:"",mobile:"",profileImage:""}),[S,C]=c.useState(!0),[j,i]=c.useState(null),[P,T]=c.useState(null);c.useEffect(()=>{a(F())},[a]),c.useEffect(()=>{r&&(B({firstName:r.firstName||"",lastName:r.lastName||"",email:r.email||"",mobile:r.mobile||"",profileImage:r.profileImage||""}),g(!1),C(!1))},[r]);const[y,N]=c.useState(!1),[O,g]=c.useState(!1);c.useEffect(()=>{y&&t&&!s&&(w.success("Profile updated successfully!"),a(M()),N(!1),a(F())),y&&d&&h&&(w.error(h.message||"Failed to update profile"),a(M()),N(!1))},[t,d,h,s,a,y]);const q=m=>{const{name:n,value:_}=m.target;B({...l,[n]:_})},V=async m=>{m.preventDefault(),N(!0);try{let n=l.profileImage;j&&(n=(await a(ee(j)).unwrap()).data.fileUrl);const _={firstName:l.firstName,lastName:l.lastName,profileImage:n};a(se(_))}catch{w.error("Failed to upload image or update profile"),N(!1)}},Y=m=>{const n=m.target.files[0];if(n){i(n),g(!1);const _=new FileReader;_.onloadend=()=>{T(_.result)},_.readAsDataURL(n)}},z=()=>{g(!0)},W=()=>{window.confirm("Are you sure you want to delete your account? This action cannot be undone.")};return e.jsx("div",{className:"BuyerProfile",children:e.jsxs(p,{icon:e.jsx(x,{className:"BuyerSidebar__icon"}),title:"My Profile",children:[e.jsxs("div",{className:"BuyerProfile__container",children:[e.jsxs("div",{className:"BuyerProfile__left-section",children:[e.jsxs("div",{className:"BuyerProfile__form-row",children:[e.jsx("div",{className:"BuyerProfile__input-field",children:e.jsxs("div",{className:"BuyerProfile__input-container",children:[e.jsx("div",{className:"BuyerProfile__input-icon",children:e.jsx(x,{})}),e.jsx("input",{type:"text",id:"firstName",name:"firstName",value:l.firstName,onChange:q,placeholder:"First Name",required:!0,className:"BuyerProfile__input"})]})}),e.jsx("div",{className:"BuyerProfile__input-field",children:e.jsxs("div",{className:"BuyerProfile__input-container",children:[e.jsx("div",{className:"BuyerProfile__input-icon",children:e.jsx(x,{})}),e.jsx("input",{type:"text",id:"lastName",name:"lastName",value:l.lastName,onChange:q,placeholder:"Last Name",required:!0,className:"BuyerProfile__input"})]})})]}),e.jsx("div",{className:"BuyerProfile__input-field",children:e.jsxs("div",{className:"BuyerProfile__input-container",children:[e.jsx("div",{className:"BuyerProfile__input-icon",children:e.jsx(Z,{})}),e.jsx("input",{type:"email",id:"email",name:"email",value:l.email,placeholder:"Email Address",className:"BuyerProfile__input BuyerProfile__input--readonly",readOnly:!0,disabled:!0})]})}),e.jsx("div",{className:"BuyerProfile__input-field",children:e.jsxs("div",{className:"BuyerProfile__input-container",children:[e.jsx("div",{className:"BuyerProfile__input-icon",children:e.jsx(J,{})}),e.jsx("input",{type:"tel",id:"mobile",name:"mobile",value:l.mobile,placeholder:"Mobile Number",className:"BuyerProfile__input BuyerProfile__input--readonly",readOnly:!0,disabled:!0})]})})]}),e.jsx("div",{className:"BuyerProfile__right-section",children:e.jsxs("div",{className:"BuyerProfile__image-container",children:[e.jsx("h3",{className:"BuyerProfile__image-title",children:"Profile Image"}),e.jsx("div",{className:"BuyerProfile__image",children:P||l.profileImage&&!O?e.jsx("img",{src:P||K(l.profileImage),alt:"Profile",onError:z}):e.jsx("div",{className:"BuyerProfile__placeholder",children:l.firstName&&l.lastName?`${l.firstName.charAt(0)}${l.lastName.charAt(0)}`:e.jsx(x,{className:"BuyerProfile__user-icon"})})}),e.jsx("button",{className:"BuyerProfile__upload-btn",onClick:()=>document.getElementById("profile-image-upload").click(),children:"Upload Photo"}),e.jsx("input",{type:"file",id:"profile-image-upload",accept:"image/*",onChange:Y,style:{display:"none"}})]})})]}),e.jsxs("div",{className:"BuyerProfile__buttons mt-30",children:[e.jsx("button",{type:"button",className:"BuyerProfile__delete-btn",onClick:W,children:"Delete Account"}),e.jsx("button",{type:"button",className:"BuyerProfile__save-btn",onClick:V,disabled:y||s,children:y||s?"Updating...":"Update & Save"})]})]})})},pe=()=>{const a=o(ae),r=s=>{switch(s.toLowerCase()){case"pdf":return e.jsx(R,{className:"BuyerDownloads__file-icon BuyerDownloads__file-icon--pdf"});case"video":return e.jsx(re,{className:"BuyerDownloads__file-icon BuyerDownloads__file-icon--video"});default:return e.jsx(R,{className:"BuyerDownloads__file-icon"})}};return e.jsx("div",{className:"BuyerDownloads",children:e.jsx(p,{icon:e.jsx(D,{className:"BuyerSidebar__icon"}),title:"My Downloads",children:a.length>0?e.jsx("div",{className:"BuyerDownloads__list",children:a.map(s=>e.jsxs("div",{className:"BuyerDownloads__item",children:[e.jsx("div",{className:"BuyerDownloads__item-icon",children:r(s.fileType)}),e.jsxs("div",{className:"BuyerDownloads__item-info",children:[e.jsx("h3",{className:"BuyerDownloads__item-title",children:s.title}),e.jsxs("p",{className:"BuyerDownloads__item-coach",children:["By ",s.coach]}),e.jsxs("div",{className:"BuyerDownloads__item-details",children:[e.jsxs("span",{className:"BuyerDownloads__item-date",children:["Downloaded: ",s.downloadDate]}),e.jsx("span",{className:"BuyerDownloads__item-size",children:s.fileSize})]})]}),e.jsxs("button",{className:"BuyerDownloads__download-btn",children:[e.jsx(D,{})," Download"]})]},s.id))}):e.jsx("div",{className:"BuyerDownloads__empty",children:e.jsx("p",{children:"You have no downloads yet."})})})})},Be=()=>{const a=o(ie),r=s=>{switch(s){case"pending":return"BuyerRequests__status--pending";case"approved":return"BuyerRequests__status--approved";case"completed":return"BuyerRequests__status--completed";default:return""}};return e.jsx("div",{className:"BuyerRequests",children:e.jsxs(p,{icon:e.jsx(U,{className:"BuyerSidebar__icon"}),title:"My Requests",children:[e.jsx("div",{className:"BuyerRequests__header",children:e.jsxs("button",{className:"BuyerRequests__add-btn",children:[e.jsx($,{})," New Request"]})}),a.length>0?e.jsx("div",{className:"BuyerRequests__list",children:a.map(s=>e.jsxs("div",{className:"BuyerRequests__item",children:[e.jsxs("div",{className:"BuyerRequests__item-header",children:[e.jsx("h3",{className:"BuyerRequests__item-title",children:s.title}),e.jsx("div",{className:`BuyerRequests__item-status ${r(s.status)}`,children:s.status.charAt(0).toUpperCase()+s.status.slice(1)})]}),e.jsx("p",{className:"BuyerRequests__item-description",children:s.description}),e.jsx("div",{className:"BuyerRequests__item-footer",children:e.jsxs("span",{className:"BuyerRequests__item-date",children:["Requested on: ",s.date]})})]},s.id))}):e.jsx("div",{className:"BuyerRequests__empty",children:e.jsx("p",{children:"You have no requests yet."})})]})})},je=()=>{const a=o(te),r=s=>{switch(s){case"active":return"BuyerBids__status--active";case"won":return"BuyerBids__status--won";case"lost":return"BuyerBids__status--lost";default:return""}};return e.jsx("div",{className:"BuyerBids",children:e.jsx(p,{icon:e.jsx(L,{className:"BuyerSidebar__icon"}),title:"My Bids",children:a.length>0?e.jsxs("div",{className:"BuyerBids__list",children:[e.jsxs("div",{className:"BuyerBids__header",children:[e.jsx("div",{className:"BuyerBids__header-item",children:"Title"}),e.jsx("div",{className:"BuyerBids__header-item",children:"Coach"}),e.jsx("div",{className:"BuyerBids__header-item",children:"Bid Amount"}),e.jsx("div",{className:"BuyerBids__header-item",children:"Date"}),e.jsx("div",{className:"BuyerBids__header-item",children:"Status"})]}),a.map(s=>e.jsxs("div",{className:"BuyerBids__item",children:[e.jsx("div",{className:"BuyerBids__item-title",children:s.title}),e.jsx("div",{className:"BuyerBids__item-coach",children:s.coach}),e.jsxs("div",{className:"BuyerBids__item-amount",children:["$",s.bidAmount.toFixed(2)]}),e.jsx("div",{className:"BuyerBids__item-date",children:s.date}),e.jsx("div",{className:`BuyerBids__item-status ${r(s.status)}`,children:s.status.charAt(0).toUpperCase()+s.status.slice(1)})]},s.id))]}):e.jsx("div",{className:"BuyerBids__empty",children:e.jsx("p",{children:"You have no bids yet."})})})})},Ne=()=>{const a=b(),r=o(le),s=o(de),t=o(ce),d=()=>{a(k(s==="list"?"add":"list")),s==="add"&&a(A())},h=i=>{window.confirm("Are you sure you want to remove this card?")&&a(me(i))},l=i=>{a(f({nameOnCard:i}))},B=i=>{a(f({cardNumber:i}))},S=i=>{a(f({expiryDate:i}))},C=i=>{a(f({cvv:i}))},j=()=>{const i={id:Date.now().toString(),lastFourDigits:t.cardNumber.slice(-4),cardType:"mastercard"};a(_e(i)),a(A()),a(k("list"))};return e.jsx("div",{className:"BuyerCards",children:e.jsx(p,{icon:e.jsx(v,{className:"BuyerSidebar__icon"}),title:"My Cards",children:s==="list"?e.jsxs("div",{className:"BuyerCards__list-view",children:[e.jsxs("div",{className:"BuyerCards__header",children:[e.jsx("h3",{className:"BuyerCards__subtitle",children:"Saved Cards"}),e.jsxs("button",{className:"BuyerCards__add-btn",onClick:d,children:[e.jsx($,{})," Add New Card"]})]}),e.jsx("div",{className:"BuyerCards__cards-list",children:r.length>0?r.map(i=>e.jsxs("div",{className:"BuyerCards__card-item",children:[e.jsxs("div",{className:"BuyerCards__card-content",children:[e.jsx("div",{className:"BuyerCards__card-logo",children:e.jsx("img",{src:"https://upload.wikimedia.org/wikipedia/commons/thumb/2/2a/Mastercard-logo.svg/200px-Mastercard-logo.svg.png",alt:"Mastercard"})}),e.jsxs("div",{className:"BuyerCards__card-number",children:["•••• •••• •••• ",i.lastFourDigits]})]}),e.jsx("button",{className:"BuyerCards__delete-btn",onClick:()=>h(i.id),"aria-label":"Delete card",children:e.jsx(ne,{})})]},i.id)):e.jsx("div",{className:"BuyerCards__empty-state",children:e.jsx("p",{children:"You have no saved payment methods yet."})})})]}):e.jsxs("div",{className:"BuyerCards__add-view",children:[e.jsx("div",{className:"BuyerCards__header",children:e.jsx("h3",{className:"BuyerCards__subtitle",children:"Add New Card"})}),e.jsx("div",{className:"BuyerCards__form",children:e.jsxs("form",{onSubmit:i=>{i.preventDefault(),j()},children:[e.jsx("div",{className:"BuyerCards__form-row",children:e.jsx("div",{className:"BuyerCards__input-field BuyerCards__input-field--full",children:e.jsxs("div",{className:"BuyerCards__input-container",children:[e.jsx("div",{className:"BuyerCards__input-icon",children:e.jsx(v,{})}),e.jsx("input",{type:"text",id:"nameOnCard",name:"nameOnCard",value:t.nameOnCard,onChange:i=>l(i.target.value),placeholder:"Name on card",required:!0,className:"BuyerCards__input"})]})})}),e.jsxs("div",{className:"BuyerCards__form-row",children:[e.jsx("div",{className:"BuyerCards__input-field BuyerCards__input-field--card-number",children:e.jsxs("div",{className:"BuyerCards__input-container",children:[e.jsx("div",{className:"BuyerCards__input-icon",children:e.jsx(v,{})}),e.jsx("input",{type:"text",id:"cardNumber",name:"cardNumber",value:t.cardNumber,onChange:i=>B(i.target.value),placeholder:"Card Number",required:!0,maxLength:19,pattern:"[0-9\\\\s]{13,19}",className:"BuyerCards__input"})]})}),e.jsx("div",{className:"BuyerCards__card-logo",children:e.jsx("img",{src:"https://upload.wikimedia.org/wikipedia/commons/thumb/2/2a/Mastercard-logo.svg/200px-Mastercard-logo.svg.png",alt:"Mastercard"})})]}),e.jsxs("div",{className:"BuyerCards__form-row",children:[e.jsx("div",{className:"BuyerCards__input-field BuyerCards__input-field--half",children:e.jsxs("div",{className:"BuyerCards__input-container",children:[e.jsx("div",{className:"BuyerCards__input-icon",children:e.jsx(oe,{})}),e.jsx("input",{type:"text",id:"expiryDate",name:"expiryDate",value:t.expiryDate,onChange:i=>S(i.target.value),placeholder:"MM/YY",required:!0,maxLength:5,pattern:"^(0[1-9]|1[0-2])\\/([0-9]{2})$",className:"BuyerCards__input"})]})}),e.jsx("div",{className:"BuyerCards__input-field BuyerCards__input-field--half",children:e.jsxs("div",{className:"BuyerCards__input-container",children:[e.jsx("div",{className:"BuyerCards__input-icon",children:e.jsx(ue,{})}),e.jsx("input",{type:"text",id:"cvv",name:"cvv",value:t.cvv,onChange:i=>C(i.target.value.replace(/\D/g,"")),placeholder:"CVV",required:!0,maxLength:4,pattern:"[0-9]{3,4}",className:"BuyerCards__input"})]})})]}),e.jsx("div",{className:"BuyerCards__form-actions",children:e.jsx("button",{type:"submit",className:"BuyerCards__submit-btn",children:"Add Card"})})]})}),e.jsx("div",{className:"BuyerCards__form-actions",children:e.jsx("button",{className:"BuyerCards__cancel-btn",onClick:d,children:"Cancel"})})]})})})},ge=()=>{const a=b(),r=he(),s=o(E);c.useEffect(()=>{const d=r.pathname;d.includes("/dashboard")?a(u("dashboard")):d.includes("/profile")?a(u("profile")):d.includes("/downloads")?a(u("downloads")):d.includes("/requests")?a(u("requests")):d.includes("/bids")?a(u("bids")):d.includes("/cards")?a(u("cards")):a(u("dashboard"))},[r.pathname,a]);const t=()=>{switch(s){case"dashboard":return e.jsx(I,{});case"profile":return e.jsx(xe,{});case"downloads":return e.jsx(pe,{});case"requests":return e.jsx(Be,{});case"bids":return e.jsx(je,{});case"cards":return e.jsx(Ne,{});default:return e.jsx(I,{})}};return e.jsx("div",{className:"BuyerAccount",children:e.jsxs("div",{className:"container max-container",children:[e.jsx("div",{className:"sidebar",children:e.jsx(ye,{})}),e.jsx("div",{className:"content",children:t()})]})})};export{ge as default};
