import{d as N,e as n,aq as S,ar as g,as as b,j as e,a5 as f,aa as w,T as m,ab as y,ac as D,at as u,au as _,av as V,aw as i,ax as F}from"./index-Bn4SSHNk.js";import{S as k}from"./SellerLayout-lvrAKPom.js";const q=()=>{const s=N(),c=n(S),t=n(g),r=n(b),o=()=>{s(u(t==="list"?"add":"list")),t==="add"&&s(_())},C=a=>{window.confirm("Are you sure you want to remove this card?")&&s(V(a))},h=a=>{s(i({nameOnCard:a}))},p=a=>{const d=a.replace(/\D/g,"").slice(0,16).replace(/(\d{4})(?=\d)/g,"$1 ");s(i({cardNumber:d}))},x=a=>{const l=a.replace(/\D/g,"");let d=l;l.length>=2&&(d=l.slice(0,2)+"/"+l.slice(2,4)),s(i({expiryDate:d}))},v=a=>{s(i({cvv:a}))},j=()=>{if(!r.nameOnCard||!r.cardNumber||!r.expiryDate||!r.cvv){alert("Please fill in all fields");return}const a={id:Date.now().toString(),lastFourDigits:r.cardNumber.replace(/\s/g,"").slice(-4),cardType:r.cardNumber.startsWith("4")?"visa":"mastercard"};s(F(a)),s(_()),s(u("list"))};return e.jsx(k,{children:e.jsx("div",{className:"SellerCards",children:t==="list"?e.jsxs("div",{className:"SellerCards__list-view",children:[e.jsxs("div",{className:"SellerCards__header",children:[e.jsx("h3",{className:"SellerCards__subtitle",children:"Saved Cards"}),e.jsxs("button",{className:"SellerCards__add-btn",onClick:o,children:[e.jsx(f,{})," Add New Card"]})]}),e.jsx("div",{className:"SellerCards__cards-list",children:c.length>0?c.map(a=>e.jsxs("div",{className:"SellerCards__card-item",children:[e.jsxs("div",{className:"SellerCards__card-content",children:[e.jsx("div",{className:"SellerCards__card-logo",children:e.jsx("img",{src:a.cardType==="visa"?"https://upload.wikimedia.org/wikipedia/commons/thumb/5/5e/Visa_Inc._logo.svg/200px-Visa_Inc._logo.svg.png":"https://upload.wikimedia.org/wikipedia/commons/thumb/2/2a/Mastercard-logo.svg/200px-Mastercard-logo.svg.png",alt:a.cardType})}),e.jsxs("div",{className:"SellerCards__card-number",children:["•••• •••• •••• ",a.lastFourDigits]})]}),e.jsx("button",{className:"SellerCards__delete-btn",onClick:()=>C(a.id),"aria-label":"Delete card",children:e.jsx(w,{})})]},a.id)):e.jsx("div",{className:"SellerCards__empty-state",children:e.jsx("p",{children:"You have no saved payment methods yet."})})})]}):e.jsxs("div",{className:"SellerCards__add-view",children:[e.jsx("div",{className:"SellerCards__header",children:e.jsx("h3",{className:"SellerCards__subtitle",children:"Add New Card"})}),e.jsx("div",{className:"SellerCards__form",children:e.jsxs("form",{onSubmit:a=>{a.preventDefault(),j()},children:[e.jsx("div",{className:"SellerCards__form-row",children:e.jsx("div",{className:"SellerCards__input-field SellerCards__input-field--full",children:e.jsxs("div",{className:"SellerCards__input-container",children:[e.jsx("div",{className:"SellerCards__input-icon",children:e.jsx(m,{})}),e.jsx("input",{type:"text",id:"nameOnCard",name:"nameOnCard",value:r.nameOnCard,onChange:a=>h(a.target.value),placeholder:"Name on card",required:!0,className:"SellerCards__input"})]})})}),e.jsx("div",{className:"SellerCards__form-row",children:e.jsx("div",{className:"SellerCards__input-field SellerCards__input-field--full",children:e.jsxs("div",{className:"SellerCards__input-container",children:[e.jsx("div",{className:"SellerCards__input-icon",children:e.jsx(m,{})}),e.jsx("input",{type:"text",id:"cardNumber",name:"cardNumber",value:r.cardNumber,onChange:a=>p(a.target.value),placeholder:"Card number",required:!0,maxLength:19,className:"SellerCards__input"})]})})}),e.jsxs("div",{className:"SellerCards__form-row",children:[e.jsx("div",{className:"SellerCards__input-field",children:e.jsxs("div",{className:"SellerCards__input-container",children:[e.jsx("div",{className:"SellerCards__input-icon",children:e.jsx(y,{})}),e.jsx("input",{type:"text",id:"expiryDate",name:"expiryDate",value:r.expiryDate,onChange:a=>x(a.target.value),placeholder:"MM/YY",required:!0,maxLength:5,className:"SellerCards__input"})]})}),e.jsx("div",{className:"SellerCards__input-field",children:e.jsxs("div",{className:"SellerCards__input-container",children:[e.jsx("div",{className:"SellerCards__input-icon",children:e.jsx(D,{})}),e.jsx("input",{type:"text",id:"cvv",name:"cvv",value:r.cvv,onChange:a=>v(a.target.value.replace(/\D/g,"")),placeholder:"CVV",required:!0,maxLength:4,pattern:"[0-9]{3,4}",className:"SellerCards__input"})]})})]}),e.jsx("div",{className:"SellerCards__form-actions",children:e.jsx("button",{type:"submit",className:"SellerCards__submit-btn",children:"Add Card"})})]})}),e.jsx("div",{className:"SellerCards__form-actions",children:e.jsx("button",{className:"SellerCards__cancel-btn",onClick:o,children:"Cancel"})})]})})})};export{q as default};
