import{r as n,e as B,K as I,j as e,N as O,O as E}from"./index-Bn4SSHNk.js";import{S as D}from"./StrategyCard-CAbD7z0_.js";import{b as L}from"./index-C3sb9xW6.js";const T=()=>{const[t,p]=n.useState(1),[u,x]=n.useState("Baseball"),[j,f]=n.useState(""),[v,b]=n.useState("high"),[c,r]=n.useState(!1),N=[{id:"fundamentals",label:"Fundamentals",checked:!0},{id:"hitting",label:"Hitting",checked:!1},{id:"pitching",label:"Pitching",checked:!1},{id:"youth-baseball",label:"Youth Baseball",checked:!1},{id:"mental-training",label:"Mental Training",checked:!1},{id:"fielding",label:"Fielding",checked:!1},{id:"strength-conditioning",label:"Strength & Conditioning",checked:!1},{id:"drills",label:"Drills",checked:!1},{id:"catching",label:"Catching",checked:!1}],[i,o]=n.useState([0,1e3]),[h,g]=n.useState({fundamentals:!0,hitting:!1,pitching:!1,"youth-baseball":!1,"mental-training":!1,fielding:!1,"strength-conditioning":!1,drills:!1,catching:!1}),k=B(I),m=()=>{r(!c)},S=s=>{s.target.classList.contains("filter-overlay")&&r(!1)};n.useEffect(()=>{const s=a=>{a.key==="Escape"&&r(!1)};return window.addEventListener("keydown",s),()=>window.removeEventListener("keydown",s)},[]);const C=s=>{g({...h,[s.target.name]:s.target.checked})},w=s=>{f(s.target.value)},y=s=>{b(s.target.value)},d=s=>{p(s)},P=()=>{g({fundamentals:!1,hitting:!1,pitching:!1,"youth-baseball":!1,"mental-training":!1,fielding:!1,"strength-conditioning":!1,drills:!1,catching:!1}),o([0,1e3])},F=()=>{const a=[];a.push(e.jsx("button",{className:"pagination-arrow",onClick:()=>t>1&&d(t-1),disabled:t===1,children:"<"},"prev"));for(let l=1;l<=7;l++)l===1||l===20||l>=t-1&&l<=t+1?a.push(e.jsx("button",{className:`pagination-item ${t===l?"active":""}`,onClick:()=>d(l),children:l},l)):(l===t-2||l===t+2)&&a.push(e.jsx("span",{className:"pagination-ellipsis",children:"..."},`ellipsis-${l}`));return a.push(e.jsx("button",{className:"pagination-arrow",onClick:()=>t<20&&d(t+1),disabled:t===20,children:">"},"next")),a};return e.jsx("div",{className:"buyer-dashboard",children:e.jsxs("div",{className:" max-container",children:[c&&e.jsx("div",{className:"filter-overlay",onClick:S}),e.jsxs("div",{className:`filter-section ${c?"drawer-open":""}`,children:[e.jsx("button",{className:"close-drawer-btn",onClick:m,children:e.jsx(O,{})}),e.jsxs("div",{className:"filter-header",children:[e.jsx("h3",{children:"Filter By"}),e.jsx("button",{className:"clear-all",onClick:P,children:"Clear All"})]}),e.jsxs("div",{className:"filter-group",children:[e.jsx("h4",{children:"Sport"}),e.jsx("div",{className:"sport-select-wrapper",children:e.jsxs("select",{className:"sport-select",value:u,onChange:s=>x(s.target.value),children:[e.jsx("option",{value:"Baseball",children:"Baseball"}),e.jsx("option",{value:"Basketball",children:"Basketball"}),e.jsx("option",{value:"Football",children:"Football"}),e.jsx("option",{value:"Soccer",children:"Soccer"}),e.jsx("option",{value:"Swimming",children:"Swimming"})]})})]}),e.jsxs("div",{className:"filter-group",children:[e.jsx("h4",{children:"Related"}),e.jsx("div",{className:"checkbox-group",children:N.map(s=>e.jsxs("div",{className:"checkbox-item",children:[e.jsx("input",{type:"checkbox",id:s.id,name:s.id,checked:h[s.id],onChange:C}),e.jsx("label",{htmlFor:s.id,children:s.label})]},s.id))})]}),e.jsxs("div",{className:"filter-group",children:[e.jsx("h4",{children:"Price"}),e.jsxs("div",{className:"price-range",children:[e.jsxs("div",{className:"price-slider-container",style:{"--min-value":i[0],"--max-value":i[1]},children:[e.jsx("input",{type:"range",min:"0",max:"1000",step:"10",value:i[0],onChange:s=>{const a=parseInt(s.target.value);a<i[1]-10&&o([a,i[1]])},className:"price-slider min-slider"}),e.jsx("input",{type:"range",min:"0",max:"1000",step:"10",value:i[1],onChange:s=>{const a=parseInt(s.target.value);a>i[0]+10&&o([i[0],a])},className:"price-slider max-slider"})]}),e.jsxs("div",{className:"price-labels",children:[e.jsxs("span",{children:["$",i[0].toLocaleString()]}),e.jsxs("span",{children:["$",i[1].toLocaleString()]})]})]})]})]}),e.jsxs("div",{className:"content-section",children:[e.jsxs("div",{className:"content-header",children:[e.jsxs("h2",{className:"content-title",children:["Featured Strategic Content ",e.jsx("span",{children:"(48 Contents Found)"})]}),e.jsxs("div",{className:"search-sort",children:[e.jsxs("div",{className:"search-container",children:[e.jsx("input",{type:"text",placeholder:"Search...",value:j,onChange:w,className:"search-input"}),e.jsx("button",{className:"search-button",children:e.jsx(L,{})})]}),e.jsx("div",{className:"sort-container",children:e.jsxs("select",{id:"sort",className:"sort-select",value:v,onChange:y,children:[e.jsx("option",{value:"high",children:"Price high to low"}),e.jsx("option",{value:"low",children:"Price low to high"}),e.jsx("option",{value:"newest",children:"Newest first"}),e.jsx("option",{value:"oldest",children:"Oldest first"})]})})]})]}),e.jsxs("button",{className:"filter-toggle-btn",onClick:m,children:["Filter ",e.jsx(E,{})]}),e.jsx("div",{className:"strategy-grid",children:k.map(s=>e.jsx(D,{id:s.id,image:s.image,title:s.title,coach:s.coach,price:s.price,hasVideo:s.hasVideo,type:s.type||"buy"},s.id))}),e.jsx("div",{className:"pagination",children:F()})]})]})})};export{T as default};
