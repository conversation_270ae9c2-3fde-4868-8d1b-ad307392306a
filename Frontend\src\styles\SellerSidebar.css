/* SellerSidebar Component Styles */
.SellerSidebar {
  display: flex;
  flex-direction: column;
  background-color: var(--white);
  border-radius: var(--border-radius);
  box-shadow: var(--box-shadow-light);
  width: 100%;
}

.SellerSidebar__container {
  display: flex;
  flex-direction: column;
  padding: var(--basefont);
}

.SellerSidebar__menu {
  display: flex;
  flex-direction: column;
  list-style: none;
  padding: 0;
  margin: 0;
}

.SellerSidebar__item {
  display: flex;
  align-items: center;
  padding: var(--smallfont);
  border-radius: var(--border-radius);
  cursor: pointer;
  transition: all 0.3s ease;
  color: var(--secondary-color);
  font-weight: 500;
}

.SellerSidebar__item:hover {
  color: var(--btn-color);
}

.SellerSidebar__item.active {
  background-color: var(--btn-color);
  color: var(--white);
  font-weight: 600;
}

.SellerSidebar__icon {
  margin-right: var(--smallfont);
  font-size: var(--heading6);
}

.SellerSidebar__logout {
  margin-top: var(--heading6);
  border-top: 1px solid var(--light-gray);
  padding-top: var(--heading6);
  color: var(--btn-color);
}

/* Mobile sidebar - Simple and Clean Approach */
.SellerSidebar.mobile {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  z-index: 1000;
  display: none;
}

.SellerSidebar.mobile.open {
  display: block;
}

.SellerSidebar__overlay {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background-color: rgba(0, 0, 0, 0.5);
  z-index: 1;
}

.SellerSidebar.mobile .SellerSidebar__container {
  position: absolute;
  top: 0;
  left: 0;
  width: 80%;
  max-width: 300px;
  height: 100%;
  background-color: var(--white);
  overflow-y: auto;
  transform: translateX(-100%);
  transition: transform 0.3s ease;
  box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
  z-index: 2;
}

.SellerSidebar.mobile.open .SellerSidebar__container {
  transform: translateX(0);
}

/* Responsive styles */
@media (max-width: 768px) {
  .SellerSidebar:not(.mobile) {
    display: none;
  }
}
