import{ay as e}from"./index-Bn4SSHNk.js";const t={position:"top-right",autoClose:5e3,hideProgressBar:!1,closeOnClick:!0,pauseOnHover:!0,draggable:!0,progress:void 0,theme:"light"},a=(s,o={})=>{e.success(s,{...t,...o})},c=(s,o={})=>{e.error(s,{...t,...o})},r=(s,o={})=>{e.warning(s,{...t,...o})},u=(s,o={})=>{e.info(s,{...t,...o})},l=(s="Loading...",o={})=>e.loading(s,{...t,...o}),g=(s,o,n="success",i={})=>{e.update(s,{render:o,type:n,isLoading:!1,...t,...i})},d=s=>{e.dismiss(s)},h=()=>{e.dismiss()},w=(s="OTP sent successfully!")=>{a(s,{autoClose:3e3})},f=(s="Failed to send OTP. Please try again.")=>{c(s,{autoClose:4e3})},P=s=>{r(`Please wait ${s} seconds before requesting another OTP`,{autoClose:s*1e3})},m=(s="OTP verified successfully!")=>{a(s,{autoClose:2e3})},p=(s="Invalid OTP. Please try again.")=>{c(s,{autoClose:4e3})},C=(s="User")=>{a(`Welcome back, ${s}!`,{autoClose:3e3})},T=()=>{a("Registration successful! Please verify your OTP.",{autoClose:4e3})},y=()=>{u("You have been logged out successfully.",{autoClose:2e3})},O=()=>{c("Network error. Please check your connection and try again.",{autoClose:6e3})},S=s=>{var n,i;let o="An unexpected error occurred. Please try again.";(i=(n=s==null?void 0:s.response)==null?void 0:n.data)!=null&&i.message?o=s.response.data.message:s!=null&&s.message&&(o=s.message),c(o,{autoClose:5e3})},k={success:a,error:c,warning:r,info:u,loading:l,update:g,dismiss:d,dismissAll:h,otp:{success:w,error:f,cooldown:P,verificationSuccess:m,verificationError:p},auth:{loginSuccess:C,registrationSuccess:T,logoutSuccess:y},network:{error:O},api:{error:S}};export{k as t};
