import{e as d,ao as c,j as e,an as l}from"./index-Bn4SSHNk.js";import{S as i}from"./SellerLayout-lvrAKPom.js";import{a as n}from"./index-CW_v2D4K.js";const x=()=>{const r=d(c);return e.jsx(i,{children:e.jsx("div",{className:"seller-requests-container",children:e.jsx("div",{className:"table-wrapper",children:e.jsxs("table",{className:"requests-table",children:[e.jsx("thead",{children:e.jsxs("tr",{children:[e.jsx("th",{children:"No."}),e.jsx("th",{children:"Request Id"}),e.jsx("th",{children:"Videos/Documents"}),e.jsx("th",{children:"Date"}),e.jsx("th",{children:"Price"}),e.jsx("th",{children:"Requested Amount"}),e.jsx("th",{children:"Requested Customer"}),e.jsx("th",{children:"Action"})]})}),e.jsx("tbody",{children:r.map((s,t)=>e.jsxs("tr",{children:[e.jsx("td",{children:t+1}),e.jsx("td",{children:s.id}),e.jsx("td",{children:e.jsxs("div",{className:"video-doc",children:[e.jsx("img",{src:s.image,alt:s.title}),e.jsx("span",{children:s.title})]})}),e.jsxs("td",{children:[s.date," | 4:50PM"]}),e.jsx("td",{children:s.price}),e.jsx("td",{children:s.requestedAmount}),e.jsx("td",{children:s.requestedCustomer}),e.jsx("td",{children:e.jsxs("div",{className:"action-icons",children:[e.jsx(l,{className:"action-icon"}),e.jsx(n,{className:"action-icon"})]})})]},s.id))})]})})})})};export{x as default};
