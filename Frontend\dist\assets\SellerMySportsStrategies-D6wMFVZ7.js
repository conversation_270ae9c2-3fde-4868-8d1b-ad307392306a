import{r,j as s,an as d}from"./index-Bn4SSHNk.js";import{S as c}from"./SellerLayout-lvrAKPom.js";const o=[{id:1,title:"<PERSON> and Coaching Philosophies...",date:"20 May 2025 | 4:50PM",price:"$22.00",status:!0,thumbnail:"video-1.jpg"},{id:2,title:"<PERSON> - Early Transition Offensive Concepts",date:"20 May 2025 | 4:50PM",price:"$22.00",status:!0,thumbnail:"video-2.jpg"},{id:3,title:`WR Fundamentals
PoA - <PERSON>`,date:"20 May 2025 | 4:50PM",price:"$22.00",status:!1,thumbnail:"video-3.jpg"},{id:4,title:"<PERSON> and Coaching Philosophies...",date:"20 May 2025 | 4:50PM",price:"$22.00",status:!0,thumbnail:"video-1.jpg"},{id:5,title:"<PERSON> - Early Transition Offensive Concepts",date:"20 May 2025 | 4:50PM",price:"$22.00",status:!1,thumbnail:"video-2.jpg"},{id:6,title:`WR Fundamentals
PoA - <PERSON> Wiggins`,date:"20 May 2025 | 4:50PM",price:"$22.00",status:!1,thumbnail:"video-3.jpg"},{id:7,title:"Frank Martin - Drills and Coaching Philosophies...",date:"20 May 2025 | 4:50PM",price:"$22.00",status:!0,thumbnail:"video-1.jpg"},{id:8,title:"John Calipari - Early Transition Offensive Concepts",date:"20 May 2025 | 4:50PM",price:"$22.00",status:!1,thumbnail:"video-2.jpg"}],u=()=>{const[a,n]=r.useState(o),l=t=>{n(e=>e.map(i=>i.id===t?{...i,status:!i.status}:i))};return s.jsx(c,{children:s.jsx("div",{className:"video-status-container",children:s.jsx("div",{className:"table-wrapper",children:s.jsxs("table",{className:"video-table",children:[s.jsx("thead",{children:s.jsxs("tr",{children:[s.jsx("th",{children:"No."}),s.jsx("th",{children:"Videos/Documents"}),s.jsx("th",{children:"Date"}),s.jsx("th",{children:"Price"}),s.jsx("th",{children:"Status"}),s.jsx("th",{children:"Action"})]})}),s.jsx("tbody",{children:a.map((t,e)=>s.jsxs("tr",{children:[s.jsx("td",{children:e+1}),s.jsx("td",{children:s.jsxs("div",{className:"video-doc",children:[s.jsx("img",{src:t.thumbnail,alt:"video thumb"}),s.jsx("span",{children:t.title})]})}),s.jsx("td",{children:t.date}),s.jsx("td",{children:t.price}),s.jsx("td",{children:s.jsxs("label",{className:"switch",children:[s.jsx("input",{type:"checkbox",checked:t.status,onChange:()=>l(t.id)}),s.jsx("span",{className:"slider round"})]})}),s.jsx("td",{children:s.jsx(d,{className:"action-icon"})})]},t.id))})]})})})})};export{u as default};
