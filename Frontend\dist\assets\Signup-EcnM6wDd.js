import{u as C,d as S,e as w,r as _,j as e,b as x,a as F,c as E,h as N,i as $,L as j,f as v,k as q,g as I,m as D}from"./index-Bn4SSHNk.js";import{F as G,G as A,f as y}from"./firebaseService-CA7fwQAS.js";import{t as l}from"./toast-JdQfOdwe.js";const z=()=>{const t=C(),c=S(),{isLoading:d,isError:L,isSuccess:P,error:U}=w(s=>s.auth),[a,m]=_.useState({firstName:"",lastName:"",email:"",phone:"",countryCode:"+91",accountType:"learn",agreeToTerms:!1}),[i,h]=_.useState({}),u=s=>{const{name:n,value:r,type:o,checked:p}=s.target;m({...a,[n]:o==="checkbox"?p:r}),i[n]&&h({...i,[n]:null})},g=s=>{m({...a,accountType:s})},f=s=>{m({...a,countryCode:s.target.value})},b=()=>{const s={};return a.firstName.trim()||(s.firstName="First name is required"),a.lastName.trim()||(s.lastName="Last name is required"),a.email.trim()?/\S+@\S+\.\S+/.test(a.email)||(s.email="Email is invalid"):s.email="Email is required",a.phone.trim()?/^\d{10}$/.test(a.phone)||(s.phone="Phone number must be 10 digits"):s.phone="Phone number is required",a.agreeToTerms||(s.agreeToTerms="You must agree to the terms and conditions"),s},T=async s=>{s.preventDefault();const n=b();if(Object.keys(n).length>0){h(n);return}c(v());try{const r={firstName:a.firstName,lastName:a.lastName,email:a.email,mobile:`${a.countryCode}${a.phone}`,role:a.accountType==="learn"?"buyer":"seller"},o=await c(q(r)).unwrap();l.auth.registrationSuccess(),t("/otp-verification",{state:{userId:o.userId,phoneNumber:`${a.countryCode} ${a.phone}`,cooldownSeconds:o.cooldownSeconds||60,isLogin:!1,developmentOtp:o.developmentOtp}})}catch(r){r.includes("already registered")?l.error("This email or mobile number is already registered. Please try logging in instead."):l.api.error({response:{data:{message:r}}})}},k=async()=>{try{if(c(v()),!y.isInitialized()){l.error("Firebase is not initialized. Please check your configuration.");return}const s=await y.signInWithGoogle();try{const n=await c(I(s.idToken)).unwrap();l.info("Account already exists. Redirecting to dashboard..."),n.user.role==="buyer"?t("/buyer/dashboard"):n.user.role==="seller"?t("/seller/dashboard"):n.user.role==="admin"?t("/admin/dashboard"):t("/")}catch(n){const r=typeof n=="string"?n:(n==null?void 0:n.message)||"";if(r.includes("not found")||r.includes("does not exist")){const o=a.accountType==="learn"?"buyer":"seller";try{const p=await c(D({idToken:s.idToken,role:o})).unwrap();l.auth.registrationSuccess(),t(o==="buyer"?"/buyer/dashboard":o==="seller"?"/seller/dashboard":"/")}catch(p){throw p}}else throw n}}catch(s){console.error("Google sign-up error:",s);const n=typeof s=="string"?s:(s==null?void 0:s.message)||"Failed to sign up with Google. Please try again.";l.error(n)}};return e.jsx("div",{className:"signup__page",children:e.jsxs("div",{className:"signup__container",children:[e.jsx("h1",{className:"signup__title",children:"Sign up to your account"}),e.jsxs("div",{className:"signup__account-type",children:[e.jsx("p",{className:"signup__label",children:"Select Account Type"}),e.jsxs("div",{className:"signup__options",children:[e.jsxs("div",{className:`signup__option ${a.accountType==="learn"?"signup__option--selected":""}`,onClick:()=>g("learn"),children:[e.jsx("div",{className:"signup__option-checkbox",children:a.accountType==="learn"&&e.jsx("div",{className:"signup__option-check",children:e.jsx(x,{className:"signup__check-icon"})})}),e.jsxs("div",{className:"signup__option-content",children:[e.jsx("div",{className:"signup__option-icon",children:e.jsx(F,{})}),e.jsx("p",{className:"signup__option-text",children:"I want to learn"})]})]}),e.jsxs("div",{className:`signup__option ${a.accountType==="teach"?"signup__option--selected":""}`,onClick:()=>g("teach"),children:[e.jsx("div",{className:"signup__option-checkbox",children:a.accountType==="teach"&&e.jsx("div",{className:"signup__option-check",children:e.jsx(x,{className:"signup__check-icon"})})}),e.jsxs("div",{className:"signup__option-content",children:[e.jsx("div",{className:"signup__option-icon",children:e.jsx(E,{})}),e.jsx("p",{className:"signup__option-text",children:"I want to teach"})]})]})]})]}),e.jsxs("form",{onSubmit:T,className:"signup__form",children:[e.jsxs("div",{className:"signup__form-row",children:[e.jsxs("div",{className:"signup__input-container",children:[e.jsx("div",{className:"signup__input-icon",children:e.jsx(N,{})}),e.jsx("input",{type:"text",id:"firstName",name:"firstName",value:a.firstName,onChange:u,placeholder:"First Name",className:`signup__input ${i.firstName?"signup__input--error":""}`,required:!0}),i.firstName&&e.jsx("p",{className:"signup__error",children:i.firstName})]}),e.jsxs("div",{className:"signup__input-container",children:[e.jsx("div",{className:"signup__input-icon",children:e.jsx(N,{})}),e.jsx("input",{type:"text",id:"lastName",name:"lastName",value:a.lastName,onChange:u,placeholder:"Last Name",className:`signup__input ${i.lastName?"signup__input--error":""}`,required:!0}),i.lastName&&e.jsx("p",{className:"signup__error",children:i.lastName})]})]}),e.jsxs("div",{className:"signup__input-container",children:[e.jsx("div",{className:"signup__input-icon",children:e.jsx($,{})}),e.jsx("input",{type:"email",id:"email",name:"email",value:a.email,onChange:u,placeholder:"Enter Email Address",className:`signup__input ${i.email?"signup__input--error":""}`,required:!0}),i.email&&e.jsx("p",{className:"signup__error",children:i.email})]}),e.jsxs("div",{className:"signup__form-input signup__phone-container",children:[e.jsxs("div",{className:"signup__phone-wrapper",children:[e.jsx("div",{children:e.jsxs("div",{className:"signup__country-code-select",children:[e.jsx(G,{style:{color:"var(--dark-gray)"}}),e.jsxs("select",{value:a.countryCode,onChange:f,className:"selectstylesnone",children:[e.jsx("option",{value:"+91",children:"+91"}),e.jsx("option",{value:"+1",children:"+1"}),e.jsx("option",{value:"+44",children:"+44"}),e.jsx("option",{value:"+61",children:"+61"}),e.jsx("option",{value:"+86",children:"+86"}),e.jsx("option",{value:"+49",children:"+49"}),e.jsx("option",{value:"+33",children:"+33"}),e.jsx("option",{value:"+81",children:"+81"}),e.jsx("option",{value:"+7",children:"+7"}),e.jsx("option",{value:"+55",children:"+55"})]})]})}),e.jsx("input",{type:"tel",id:"phone",name:"phone",value:a.phone,onChange:s=>{const n=s.target.value.replace(/\D/g,"");u({target:{name:"phone",value:n}})},placeholder:"00000 00000",className:`signup__form-input signup__phone-input ${i.phone?"signup__input--error":""}`,required:!0,pattern:"[0-9]*"})]}),i.phone&&e.jsx("p",{className:"error-message",children:i.phone})]}),e.jsxs("div",{className:"signup__terms",children:[e.jsx("input",{type:"checkbox",id:"agreeToTerms",name:"agreeToTerms",checked:a.agreeToTerms,onChange:u,className:"signup__checkbox"}),e.jsxs("label",{htmlFor:"agreeToTerms",className:"signup__terms-label",children:["By sign up you agree to our"," ",e.jsx(j,{to:"/terms",className:"signup__terms-link",children:"Terms & Conditions"})]}),i.agreeToTerms&&e.jsx("p",{className:"signup__error",children:i.agreeToTerms})]}),e.jsx("button",{type:"submit",className:"signup__button",disabled:d,children:d?"Creating Account...":"Create Your Account"}),e.jsx("div",{className:"signup__divider",children:e.jsx("span",{children:"or"})}),e.jsx(A,{onClick:k,isLoading:d,text:"Sign up with Google",variant:"secondary"}),e.jsxs("p",{className:"signup__login-link mt-10",children:["Do you have an account?"," ",e.jsx(j,{to:"/auth",className:"signup__link",children:"Sign In"})]})]})]})})};export{z as default};
