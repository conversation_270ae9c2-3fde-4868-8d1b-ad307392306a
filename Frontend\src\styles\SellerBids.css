/* SellerBids Component Styles */
.seller-bids-container {
  padding: var(--section-padding);
  background-color: var(--white);
  font-family: "Poppins", sans-serif;
}

.bids-table {
  width: 100%;
  font-size: var(--basefont);
  background-color: var(--white);
  border: 1px solid var(--light-gray);
  border-radius: var(--border-radius-large);
  overflow: hidden;
}

.bids-table th {
  padding: 12px 10px;
  text-align: left;
  vertical-align: middle;
}

.bids-table td {
  padding: 12px 10px;
  text-align: left;
  border-top: 1px solid var(--light-gray);
  vertical-align: middle;
}

.video-doc {
  display: flex;
  align-items: center;
  gap: 10px;
}

.video-doc img {
  width: 40px;
  height: 40px;
  border-radius: var(--border-radius);
  object-fit: cover;
}

.video-doc span {
  font-size: var(--basefont);
  font-weight: 500;
  color: var(--text-color);
}

.action-icon {
  font-size: 30px;
  color: black;
  cursor: pointer;
  transition: all 0.2s ease;
  padding: 6px;
  border-radius: 6px;
  display: flex;
  align-items: center;
  justify-content: center;
  background-color: transparent;
}

.action-icon:hover {
  color: var(--primary-color);
  background-color: var(--primary-light-color);
  transform: translateY(-2px);
}
