import{e as i,ap as l,j as s}from"./index-Bn4SSHNk.js";import{S as c}from"./SellerLayout-lvrAKPom.js";import{a as t}from"./index-BxutIMF0.js";const x=()=>{const d=i(l);return s.jsx(c,{children:s.jsx("div",{className:"seller-bids-container",children:s.jsx("div",{className:"table-wrapper",children:s.jsxs("table",{className:"bids-table",children:[s.jsx("thead",{children:s.jsxs("tr",{children:[s.jsx("th",{children:"No."}),s.jsx("th",{children:"Bid Id"}),s.jsx("th",{children:"Videos/Documents"}),s.jsx("th",{children:"Date"}),s.jsx("th",{children:"Price"}),s.jsx("th",{children:"Bid Amount"}),s.jsx("th",{children:"Action"})]})}),s.jsx("tbody",{children:d.map((e,r)=>s.jsxs("tr",{children:[s.jsx("td",{children:r+1}),s.jsx("td",{children:e.id}),s.jsx("td",{children:s.jsxs("div",{className:"video-doc",children:[s.jsx("img",{src:e.image,alt:e.title}),s.jsx("span",{children:e.title})]})}),s.jsxs("td",{children:[e.date," | 4:50PM"]}),s.jsx("td",{children:e.price}),s.jsx("td",{children:e.bidAmount}),s.jsx("td",{children:s.jsx(t,{className:"action-icon"})})]},e.id))})]})})})})};export{x as default};
