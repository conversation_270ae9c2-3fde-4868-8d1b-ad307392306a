import React, { useEffect, useState } from "react";
import { useSelector, useDispatch } from "react-redux";
import { useLocation } from "react-router-dom";
import {
  selectActiveTab,
  selectIsSidebarOpen,
  setActiveTab,
  setSidebarOpen,
} from "../../redux/slices/sellerDashboardSlice";
import SellerSidebar from "./SellerSidebar";
import "../../styles/SellerLayout.css";

// Icons
import { MdDashboard } from "react-icons/md";
import { FaUser } from "react-icons/fa";
import { MdRequestPage } from "react-icons/md";
import { FaGavel } from "react-icons/fa";
import { MdVideoLibrary } from "react-icons/md";
import { FaCreditCard } from "react-icons/fa";
import { RiMenu5Line } from "react-icons/ri";

const SellerLayout = ({ children }) => {
  const dispatch = useDispatch();
  const location = useLocation();
  const activeTab = useSelector(selectActiveTab);
  const isSidebarOpen = useSelector(selectIsSidebarOpen);
  const [isMobile, setIsMobile] = useState(false);

  // Check if screen is mobile size
  useEffect(() => {
    const checkMobile = () => {
      setIsMobile(window.innerWidth <= 768);
    };

    checkMobile();
    window.addEventListener('resize', checkMobile);

    return () => window.removeEventListener('resize', checkMobile);
  }, []);

  // Toggle mobile sidebar
  const toggleMobileSidebar = () => {
    dispatch(setSidebarOpen(!isSidebarOpen));
  };

  // Map routes to tabs
  const routeToTabMap = {
    "/seller/dashboard": "dashboard",
    "/seller/my-sports-strategies": "my-sports-strategies",
    "/seller/requests": "requests",
    "/seller/bids": "bids",
    "/seller/cards": "cards",
    "/seller/profile": "profile",
  };

  // Header configuration for each page
  const headerConfig = {
    "dashboard": {
      title: "Dashboard",

      icon: <MdDashboard />
    },
    "my-sports-strategies": {
      title: "My Sports Strategies",

      icon: <MdVideoLibrary />
    },
    "requests": {
      title: "Requests",

      icon: <MdRequestPage />
    },
    "bids": {
      title: "Bids",

      icon: <FaGavel />
    },
    "cards": {
      title: "My Cards",

      icon: <FaCreditCard />
    },
    "profile": {
      title: "My Profile",

      icon: <FaUser />
    }
  };

  // Get current header info
  const currentHeader = headerConfig[activeTab] || headerConfig["dashboard"];

  // Update active tab based on current route
  useEffect(() => {
    const currentTab = routeToTabMap[location.pathname];
    if (currentTab && currentTab !== activeTab) {
      dispatch(setActiveTab(currentTab));
    }
  }, [location.pathname, activeTab, dispatch]);

  return (
    <div className="SellerLayout">
      <div className="container max-container">
        {/* Desktop Sidebar */}
        {!isMobile && (
          <div className="sidebar">
            <SellerSidebar />
          </div>
        )}

        <div className="content">
          <div className="bordrdiv mb-30">
            {/* Mobile hamburger menu */}
            {isMobile && (
              <button
                className="SellerLayout__mobile-toggle"
                onClick={toggleMobileSidebar}
                aria-label="Toggle sidebar"
              >
                <RiMenu5Line />
              </button>
            )}

            <h2 className="SellerLayout__title">
              {currentHeader.icon}
              {currentHeader.title}
            </h2>
          </div>
          <div className="SellerLayout__content">
            {children}
          </div>
        </div>
      </div>

      {/* Mobile Sidebar */}
      {isMobile && (
        <SellerSidebar isMobile={true} />
      )}
    </div>
  );
};

export default SellerLayout;
